# 接口异常处理最佳实践规范

## 概述

本规范基于jeecg-boot框架，定义了后端接口开发中异常处理的标准做法，确保代码质量和用户体验的一致性。

## 核心原则

### 1. 统一异常处理
- **使用全局异常处理器**：依赖 `JeecgBootExceptionHandler` 统一处理异常
- **避免手动try-catch**：除特殊场景外，不在Controller层手动捕获异常
- **标准化异常类型**：使用框架提供的异常类型，确保统一的响应格式

### 2. 异常分层处理
- **Controller层**：只负责参数接收和方法调用，不处理业务异常
- **Service层**：处理业务逻辑验证，抛出业务异常
- **全局异常处理器**：统一捕获和格式化异常响应

## 异常类型规范

### 1. 业务异常
**使用场景**：业务逻辑验证失败、数据状态不符合要求等
```java
// ✅ 正确做法
throw new JeecgBootException("记录不存在");
throw new JeecgBootException("只能调整可用状态的补助金过期时间");

// ❌ 错误做法
throw new RuntimeException("记录不存在");
throw new Exception("业务异常");
```

### 2. 参数验证异常
**使用场景**：使用@Valid注解进行参数验证
```java
// ✅ 正确做法 - 使用Bean Validation
@PostMapping("/create")
public Result<?> create(@Valid @RequestBody CreateVO createVO) {
    // 框架自动处理验证异常
}

// VO类中定义验证规则
public class CreateVO {
    @NotBlank(message = "名称不能为空")
    private String name;
    
    @NotNull(message = "金额不能为空")
    @DecimalMin(value = "0.01", message = "金额必须大于0")
    private BigDecimal amount;
}
```

### 3. 系统异常
**使用场景**：数据库连接失败、网络异常等系统级错误
```java
// ✅ 正确做法 - 让框架处理
// 系统异常通常不需要手动抛出，由框架自动捕获处理
```

## Controller层规范

### ✅ 推荐写法
```java
@PostMapping("/adjustExpireTime")
public Result<?> adjustExpireTime(@Valid @RequestBody AdjustExpireTimeVO vo) {
    // 直接调用Service方法，异常交给全局处理器
    memberService.adjustExpireTime(vo);
    return Result.ok("操作成功");
}
```

### ❌ 不推荐写法
```java
@PostMapping("/adjustExpireTime")
public Result<?> adjustExpireTime(@Valid @RequestBody AdjustExpireTimeVO vo) {
    try {
        memberService.adjustExpireTime(vo);
        return Result.ok("操作成功");
    } catch (Exception e) {
        log.error("操作失败", e);
        return Result.error("操作失败：" + e.getMessage());
    }
}
```

## Service层规范

### ✅ 推荐写法
```java
@Override
@Transactional(rollbackFor = Exception.class)
public void adjustExpireTime(AdjustExpireTimeVO vo) {
    // 1. 数据验证
    MemberAccountCapital record = this.getById(vo.getId());
    if (record == null) {
        throw new JeecgBootException("记录不存在");
    }
    
    // 2. 业务逻辑验证
    if (!"0".equals(record.getStatus())) {
        throw new JeecgBootException("只能调整可用状态的记录");
    }
    
    // 3. 执行业务操作
    boolean result = this.updateById(record);
    if (!result) {
        throw new JeecgBootException("更新失败");
    }
    
    // 4. 记录操作日志（可选）
    logService.recordOperation("调整过期时间", vo.getId());
}
```

### ❌ 不推荐写法
```java
@Override
public void adjustExpireTime(AdjustExpireTimeVO vo) {
    try {
        MemberAccountCapital record = this.getById(vo.getId());
        if (record == null) {
            throw new RuntimeException("记录不存在");
        }
        // ... 业务逻辑
    } catch (Exception e) {
        log.error("业务处理失败", e);
        throw e; // 重新抛出异常
    }
}
```

## 特殊场景处理

### 1. 需要手动try-catch的场景
```java
// ✅ 场景1：保护旧业务逻辑不受新功能影响
@Override
public void complexBusinessMethod() {
    // 执行核心业务逻辑
    coreBusinessLogic();
    
    // 新增的非关键功能，失败不影响主流程
    try {
        newFeatureLogic();
    } catch (Exception e) {
        log.warn("新功能执行失败，不影响主流程", e);
    }
}

// ✅ 场景2：需要特殊异常处理逻辑
@Override
public void specialHandlingMethod() {
    try {
        riskyOperation();
    } catch (SpecificException e) {
        // 针对特定异常的特殊处理
        handleSpecificException(e);
        throw new JeecgBootException("操作失败，已进行补偿处理");
    }
}
```

### 2. 异步操作异常处理
```java
// ✅ 异步方法中的异常处理
@Async
public void asyncMethod() {
    try {
        // 异步业务逻辑
        doAsyncWork();
    } catch (Exception e) {
        log.error("异步操作失败", e);
        // 异步方法中的异常需要手动处理，因为无法传递给Controller
    }
}
```

## 异常信息规范

### 1. 异常消息编写原则
- **用户友好**：面向用户的错误信息要清晰易懂
- **信息充分**：包含足够的上下文信息便于排查
- **避免敏感信息**：不暴露系统内部实现细节

```java
// ✅ 好的异常消息
throw new JeecgBootException("记录不存在，请检查记录ID是否正确");
throw new JeecgBootException("只能调整可用状态的补助金过期时间");
throw new JeecgBootException("新过期时间不能早于当前时间");

// ❌ 不好的异常消息
throw new JeecgBootException("error");
throw new JeecgBootException("SQL执行失败：SELECT * FROM ...");
throw new JeecgBootException("NullPointerException");
```

### 2. 国际化支持
```java
// ✅ 支持国际化的异常消息
throw new JeecgBootException(messageSource.getMessage("record.not.found", null, locale));
```

## 日志记录规范

### 1. 异常日志级别
```java
// ✅ 正确的日志级别使用
log.error("系统异常，需要立即处理", e);        // 系统级错误
log.warn("业务异常，用户操作不当", e);         // 业务异常
log.info("正常业务流程记录");                // 正常流程
log.debug("调试信息：参数值={}", param);      // 调试信息
```

### 2. 异常日志内容
```java
// ✅ 完整的异常日志
log.error("调整过期时间失败，记录ID：{}，用户ID：{}，原因：{}", 
         vo.getId(), getCurrentUserId(), e.getMessage(), e);
```

## 全局异常处理器配置

### 1. 自定义异常处理
```java
@RestControllerAdvice
public class CustomExceptionHandler {
    
    @ExceptionHandler(JeecgBootException.class)
    public Result<?> handleBusinessException(JeecgBootException e) {
        log.warn("业务异常：{}", e.getMessage());
        return Result.error(e.getMessage());
    }
    
    @ExceptionHandler(MethodArgumentNotValidException.class)
    public Result<?> handleValidationException(MethodArgumentNotValidException e) {
        String message = e.getBindingResult().getFieldError().getDefaultMessage();
        return Result.error(message);
    }
}
```

## 最佳实践检查清单

### Controller层检查项
- [ ] 是否避免了不必要的try-catch
- [ ] 是否使用了@Valid进行参数验证
- [ ] 是否让异常自然向上抛出

### Service层检查项
- [ ] 是否使用JeecgBootException抛出业务异常
- [ ] 异常消息是否用户友好且信息充分
- [ ] 是否在事务方法上添加了@Transactional注解
- [ ] 是否避免了捕获后重新抛出的无意义操作

### 异常消息检查项
- [ ] 消息是否清晰描述了问题
- [ ] 是否避免了技术细节暴露
- [ ] 是否提供了解决建议（如适用）

## 总结

遵循本规范可以确保：
1. **代码简洁**：减少冗余的异常处理代码
2. **响应统一**：通过全局异常处理器保证API响应格式一致
3. **维护性好**：异常处理逻辑集中管理，便于维护
4. **用户体验佳**：提供清晰友好的错误信息

---

**文档维护**：
- 创建时间：2025-01-18
- 最后更新：2025-01-18
- 维护人员：开发团队
