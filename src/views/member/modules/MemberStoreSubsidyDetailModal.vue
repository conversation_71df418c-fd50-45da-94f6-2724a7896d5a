<template>
  <a-modal :title="title" :width="1200" :visible="visible" cancelText="关闭" :footer="null" @cancel="handleCancel">
    <a-card :bordered="false">
      <!-- 说明区域 -->
      <a-alert 
        message="店铺补助金明细说明" 
        description="此页面显示该会员所有店铺补助金相关的交易记录，包括：发放记录(50)、消费/抵扣记录(51)、退回/恢复记录(52)" 
        type="info" 
        show-icon 
        style="margin-bottom: 16px"
      />
      
      <!-- 查询区域 -->
      <div class="table-page-search-wrapper">
        <a-form layout="inline">
          <a-row :gutter="24">
            <a-col :md="8" :sm="8">
              <a-form-item label="流水号">
                <a-input placeholder="请输入流水号" v-model="queryParam.orderNo"></a-input>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="8">
              <a-form-item label="交易单号">
                <a-input placeholder="请输入交易单号" v-model="queryParam.tradeNo"></a-input>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="8">
              <a-form-item label="补助店铺">
                <a-input placeholder="请输入店铺名称" v-model="queryParam.subsidyStoreName"></a-input>
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="8">
              <a-form-item label="补助金状态">
                <j-dict-select-tag v-model="queryParam.storeSubsidyStatus" placeholder="请选择" dictCode="store_subsidy_status" />
              </a-form-item>
            </a-col>
            <a-col :md="8" :sm="8">
              <a-form-item label="交易时间">
                <a-range-picker
                  style="min-width: 210px"
                  v-model="queryParam.createTime"
                  format="YYYY-MM-DD"
                  :placeholder="['开始时间', '结束时间']"
                  @change="onDateChange"
                />
              </a-form-item>
            </a-col>

            <a-col :md="24" :sm="8">
              <span style="float: left;overflow: hidden;" class="table-page-search-submitButtons">
                <a-button type="primary" @click="searchQuery" icon="search">查询</a-button>
                <a-button type="primary" @click="searchReset" icon="reload" style="margin-left: 8px">重置</a-button>
              </span>
            </a-col>
          </a-row>
        </a-form>
      </div>

      <!-- 操作按钮区域 -->
      <div class="table-operator">
        <!--      <a-button @click="handleAdd" type="primary" icon="plus">新增</a-button>-->
        <!--<a-button type="primary" icon="download"  :loading="exportLoading"  @click="handleExportXls('收货地址')">导出</a-button>-->
      </div>
      <!-- table区域-begin -->
      <a-table
        ref="table"
        size="middle"
        rowKey="id"
        bordered
        :columns="columns"
        :dataSource="dataSource"
        :pagination="ipagination"
        :loading="loading"
        :rowSelection="{ selectedRowKeys: selectedRowKeys, onChange: onSelectChange }"
        @change="handleTableChange"
      >
        <template slot="goAndCome" slot-scope="text, record">
          <div v-if="record.goAndCome == 0">收入</div>
          <div v-if="record.goAndCome == 1">支出</div>
        </template>
        <template slot="storeSubsidyStatus" slot-scope="text, record">
          <!-- 只有店铺补助金相关记录才显示状态 -->
          <span v-if="['50', '51', '52'].includes(record.payType)">
            <a-tag v-if="record.storeSubsidyStatus == '0'" color="green">可用</a-tag>
            <a-tag v-else-if="record.storeSubsidyStatus == '1'" color="red">已过期</a-tag>
            <a-tag v-else-if="record.storeSubsidyStatus == '2'" color="gray">已用尽</a-tag>
            <span v-else>-</span>
          </span>
          <span v-else>-</span>
        </template>
        <template slot="action" slot-scope="text, record">
          <a-button
            v-if="showAdjustButton(record)"
            type="link"
            size="small"
            @click="handleAdjustExpireTime(record)">
            调整过期时间
          </a-button>
          <span v-else>-</span>
        </template>
      </a-table>

      <!-- table区域-end -->

      <!-- 表单区域 -->
    </a-card>

    <!-- 调整过期时间弹窗 -->
    <adjust-expire-time-modal ref="adjustExpireTimeModal" @success="handleAdjustSuccess" />
  </a-modal>
</template>
<script>
import { JeecgListMixin } from '@/mixins/JeecgListMixin'
import { filterObj } from '@/utils/util'
import JInput from '@/components/jeecg/JInput.vue'
import AdjustExpireTimeModal from './AdjustExpireTimeModal.vue'
export default {
  name: 'MemberStoreSubsidyDetailModal',
  mixins: [JeecgListMixin],
  components: {
    JInput,
    AdjustExpireTimeModal
  },
  data() {
    return {
      model: {},
      visible: false,
      title: '店铺补助金明细',
      // 表头
      columns: [
        {
          title: '#',
          dataIndex: '',
          key: 'rowIndex',
          width: 60,
          align: 'center',
          customRender: function(t, r, index) {
            return parseInt(index) + 1
          }
        },
        {
          title: '流水号',
          align: 'center',
          dataIndex: 'orderNo'
        },
        {
          title: '交易类型',
          align: 'center',
          dataIndex: 'payType_dictText'
        },
        {
          title: '收入/支出',
          align: 'center',
          dataIndex: 'goAndCome',
          scopedSlots: { customRender: 'goAndCome' }
        },
        {
          title: '交易金额',
          align: 'center',
          dataIndex: 'amount'
        },
        {
          title: '补助店铺名称',
          align: 'center',
          dataIndex: 'subsidyStoreName',
          customRender: (text, record) => {
            // 只有店铺补助金相关记录才显示店铺名称
            if (['50', '51', '52'].includes(record.payType)) {
              return text || '-'
            }
            return '-'
          }
        },
        {
          title: '剩余补助金额',
          align: 'center',
          dataIndex: 'remainingAmount',
          customRender: (text, record) => {
            // 只有发放类型(50)的记录才有剩余金额概念
            if (record.payType === '50') {
              return text || '0.00'
            }
            return '-'
          }
        },
        {
          title: '补助金过期时间',
          align: 'center',
          dataIndex: 'expireTime',
          customRender: (text, record) => {
            // 只有发放类型(50)的记录才有过期时间
            if (record.payType === '50') {
              return text || '-'
            }
            return '-'
          }
        },
        {
          title: '店铺补助金状态',
          align: 'center',
          dataIndex: 'storeSubsidyStatus',
          scopedSlots: { customRender: 'storeSubsidyStatus' }
        },
        {
          title: '交易时间',
          align: 'center',
          dataIndex: 'createTime'
        },
        {
          title: '交易单号',
          align: 'center',
          dataIndex: 'tradeNo'
        },
        {
          title: '备注',
          align: 'center',
          dataIndex: 'remarks'
        },
        {
          title: '操作',
          align: 'center',
          dataIndex: 'action',
          width: 120,
          scopedSlots: { customRender: 'action' }
        }
      ],
      isSetPageNo: false,
      url: {
        list: '/memberAccountCapital/memberAccountCapital/queryPageListByMemberId'
      }
    }
  },
  computed: {
    importExcelUrl: function() {
      return `${window._CONFIG['domianURL']}/${this.url.importExcelUrl}`
    }
  },
  methods: {
    onDateChange: function(value, dateString) {
      this.queryParam.createTime_begin = dateString[0]
      this.queryParam.createTime_end = dateString[1]
    },
    getQueryParams() {
      var param = Object.assign({}, this.queryParam, this.isorter, { 
        memberListId: this.model.id,
        payType: '50,51,52'  // 使用payType字段，传递店铺补助金相关的所有交易类型
      })
      
      param.field = this.getQueryField()
      param.pageNo = this.ipagination.current
      param.pageSize = this.ipagination.pageSize
      delete param.createTime // 时间参数不传递后台
      
      return filterObj(param)
    },
    async showModal(record) {
      this.model = Object.assign({}, record)
      this.visible = true
      await this.$nextTick()
      this.loadData()
    },
    handleCancel() {
      this.visible = false
      this.ipagination.current = 1
    },
    /**
     * 判断是否显示调整过期时间按钮
     * @param record 记录对象
     * @returns {boolean} 是否显示按钮
     */
    showAdjustButton(record) {
      // 显示条件：
      // 1. 交易类型为店铺补助金发放(50)
      // 2. 交易方向为收入(0)
      // 3. 补助金状态为可用(0)
      // 4. 剩余金额大于0
      return record.payType === '50' &&
             record.goAndCome === '0' &&
             record.storeSubsidyStatus === '0' &&
             record.remainingAmount &&
             parseFloat(record.remainingAmount) > 0
    },
    /**
     * 处理调整过期时间按钮点击事件
     * @param record 记录对象
     */
    handleAdjustExpireTime(record) {
      this.$refs.adjustExpireTimeModal.show(record)
    },
    /**
     * 处理调整成功事件
     */
    handleAdjustSuccess() {
      // 刷新列表数据
      this.loadData()
      this.$message.success('过期时间调整成功')
    }
  }
}
</script>
<style lang="sass" scoped></style> 