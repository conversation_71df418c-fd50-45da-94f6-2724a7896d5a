<template>
  <a-modal
    :title="title"
    :visible="visible"
    :confirmLoading="confirmLoading"
    width="600px"
    @ok="handleSubmit"
    @cancel="handleCancel"
    :maskClosable="false"
    :keyboard="false"
  >
    <a-form-model ref="form" :model="form" :rules="rules" :label-col="{ span: 6 }" :wrapper-col="{ span: 18 }">
      <a-form-model-item label="流水号">
        <span>{{ currentRecord.orderNo || '-' }}</span>
      </a-form-model-item>
      <a-form-model-item label="补助店铺">
        <span>{{ currentRecord.subsidyStoreName || '-' }}</span>
      </a-form-model-item>
      <a-form-model-item label="剩余金额">
        <span>{{ currentRecord.remainingAmount || '0.00' }}元</span>
      </a-form-model-item>
      <a-form-model-item label="当前过期时间">
        <span>{{ currentRecord.expireTime || '-' }}</span>
      </a-form-model-item>
      <a-form-model-item label="新过期时间" prop="newExpireTime">
        <a-date-picker
          v-model="form.newExpireTime"
          format="YYYY-MM-DD HH:mm:ss"
          show-time
          placeholder="请选择新的过期时间"
          style="width: 100%"
          :disabled="confirmLoading"
          :disabledDate="disabledDate"
        />
      </a-form-model-item>
      <a-form-model-item label="调整原因" prop="reason">
        <a-textarea
          v-model="form.reason"
          placeholder="请输入调整原因（如：根据业务需要延长补助金有效期）"
          :rows="4"
          :maxlength="200"
          show-count
          :disabled="confirmLoading"
        />
      </a-form-model-item>
    </a-form-model>
  </a-modal>
</template>

<script>
import { postAction } from '@/api/manage'
import moment from 'moment'

export default {
  name: 'AdjustExpireTimeModal',
  data() {
    return {
      title: '调整过期时间',
      visible: false,
      confirmLoading: false,
      currentRecord: {},
      form: {
        newExpireTime: null,
        reason: ''
      },
      rules: {
        newExpireTime: [
          { required: true, message: '请选择新的过期时间', trigger: 'change' },
          { validator: this.validateExpireTime, trigger: 'change' }
        ],
        reason: [
          { required: true, message: '请输入调整原因', trigger: 'blur' },
          { min: 5, max: 200, message: '调整原因长度在5到200个字符之间', trigger: 'blur' }
        ]
      }
    }
  },
  methods: {
    /**
     * 显示弹窗
     * @param record 要调整的记录
     */
    show(record) {
      this.currentRecord = Object.assign({}, record)
      this.visible = true
      this.resetForm()
    },
    /**
     * 隐藏弹窗
     */
    hide() {
      this.visible = false
      this.resetForm()
    },
    /**
     * 重置表单
     */
    resetForm() {
      this.form = {
        newExpireTime: null,
        reason: ''
      }
      this.$nextTick(() => {
        if (this.$refs.form) {
          this.$refs.form.clearValidate()
        }
      })
    },
    /**
     * 禁用日期
     */
    disabledDate(current) {
      // 禁用今天之前的日期
      return current && current < moment().startOf('day')
    },
    /**
     * 验证过期时间
     */
    validateExpireTime(rule, value, callback) {
      if (!value) {
        callback(new Error('请选择新的过期时间'))
        return
      }

      const now = moment()
      const selectedTime = moment(value)

      if (selectedTime.isBefore(now)) {
        callback(new Error('新过期时间不能早于当前时间'))
        return
      }

      // 检查是否与当前过期时间相同
      if (this.currentRecord.expireTime) {
        const currentExpireTime = moment(this.currentRecord.expireTime)
        if (selectedTime.isSame(currentExpireTime, 'minute')) {
          callback(new Error('新过期时间不能与当前过期时间相同'))
          return
        }
      }

      callback()
    },
    /**
     * 处理表单提交
     */
    handleSubmit() {
      this.$refs.form.validate((valid) => {
        if (valid) {
          this.confirmLoading = true

          const params = {
            id: this.currentRecord.id,
            newExpireTime: moment(this.form.newExpireTime).format('YYYY-MM-DD HH:mm:ss'),
            reason: this.form.reason
          }

          postAction('/memberAccountCapital/memberAccountCapital/adjustExpireTime', params)
            .then((res) => {
              if (res.success) {
                this.$message.success('调整成功')
                this.hide()
                this.$emit('success')
              } else {
                this.$message.error(res.message || '调整失败')
              }
            })
            .catch((error) => {
              console.error('调整过期时间失败', error)
              this.$message.error('调整失败，请稍后重试')
            })
            .finally(() => {
              this.confirmLoading = false
            })
        } else {
          console.log('表单验证失败')
        }
      })
    },
    /**
     * 处理取消操作
     */
    handleCancel() {
      this.hide()
    }
  }
}
</script>

<style scoped>
/* 自定义样式 */
</style>
