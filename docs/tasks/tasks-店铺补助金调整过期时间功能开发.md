# 店铺补助金调整过期时间功能开发任务计划

**项目**: 八闽助业集市管理后台  
**功能**: 会员余额明细页面店铺补助金调整过期时间  
**开发周期**: 预计2-3天  
**创建时间**: 2025-01-18

## 项目背景

在会员余额明细页面的店铺补助金明细弹窗中，为店铺补助金发放记录新增"调整过期时间"功能，允许管理员调整符合条件的补助金过期时间。

## 功能需求

### 核心功能
1. **操作按钮显示条件**：
   - 交易类型为发放记录(代码值：50)
   - 交易方向为收入(goAndCome=0)
   - 补助金状态为可用(storeSubsidyStatus=0)
   - 剩余金额大于0(remainingAmount>0)

2. **调整功能**：
   - 显示当前过期时间
   - 选择新的过期时间（必须大于当前时间）
   - 填写调整原因（必填）
   - 提交后更新数据库并刷新列表

## 技术架构

### 前端技术栈
- **框架**: Vue 2.6.10
- **UI组件**: Ant Design Vue 1.7.2
- **表单组件**: a-form-model（Vue2专用）
- **网络请求**: postAction（jeecg-boot规范）

### 后端技术栈
- **框架**: jeecg-boot 3.4.3
- **接口**: POST /memberAccountCapital/adjustExpireTime
- **数据表**: member_account_capital
- **字段更新**: expire_time

## 详细任务分解

### 阶段一：前端代码分析与准备

#### 任务1.1：分析组件结构和数据流
- **描述**: 使用codebase-retrieval工具深入分析MemberStoreSubsidyDetailModal.vue
- **输出**: 理解组件结构、数据流、事件处理机制
- **预计工时**: 0.5天

#### 任务1.2：理解现有表格配置
- **描述**: 分析columns数组结构和自定义渲染逻辑
- **输出**: 掌握如何正确添加操作列
- **预计工时**: 0.2天

#### 任务1.3：研究业务逻辑判断条件
- **描述**: 理解店铺补助金状态判断逻辑
- **输出**: 确定显示调整按钮的准确条件
- **预计工时**: 0.3天

### 阶段二：后端接口设计与开发

#### 任务2.1：设计接口参数和VO类
- **描述**: 设计AdjustExpireTimeVO类，定义参数和验证规则
- **文件**: AdjustExpireTimeVO.java
- **字段**: 
  - id: 记录ID（必填）
  - newExpireTime: 新过期时间（必填，大于当前时间）
  - reason: 调整原因（必填，最大200字符）
- **预计工时**: 0.3天

#### 任务2.2：开发Controller层接口
- **描述**: 在MemberAccountCapitalController中新增/adjustExpireTime接口
- **功能**: 参数接收、基本验证、调用Service层
- **预计工时**: 0.2天

#### 任务2.3：实现Service层业务逻辑
- **描述**: 在MemberAccountCapitalService中实现adjustExpireTime方法
- **功能**: 
  - 数据验证（记录存在性、权限检查）
  - 业务逻辑验证（时间合理性、状态检查）
  - 数据更新（更新expire_time字段）
- **预计工时**: 0.4天

#### 任务2.4：添加操作日志记录
- **描述**: 实现调整操作的日志记录功能
- **功能**: 记录操作人、操作时间、调整前后值、调整原因
- **预计工时**: 0.2天

### 阶段三：前端UI改造实现

#### 任务3.1：在columns中新增操作列
- **描述**: 在MemberStoreSubsidyDetailModal.vue的columns数组中新增操作列
- **配置**: 
  - title: '操作'
  - align: 'center'
  - scopedSlots: { customRender: 'action' }
- **预计工时**: 0.1天

#### 任务3.2：添加操作列模板
- **描述**: 在template中添加action slot
- **功能**: 按条件显示调整按钮
- **预计工时**: 0.2天

#### 任务3.3：实现业务判断方法
- **描述**: 实现showAdjustButton方法
- **逻辑**: 判断记录是否符合显示调整按钮的条件
- **预计工时**: 0.2天

#### 任务3.4：添加事件处理方法
- **描述**: 实现handleAdjustExpireTime方法
- **功能**: 处理调整按钮点击事件，打开调整弹窗
- **预计工时**: 0.1天

### 阶段四：调整弹窗组件开发

#### 任务4.1：创建AdjustExpireTimeModal.vue文件
- **描述**: 创建新的Vue组件文件，搭建基本模板结构
- **位置**: src/views/member/modules/AdjustExpireTimeModal.vue
- **预计工时**: 0.2天

#### 任务4.2：设计表单结构和验证规则
- **描述**: 使用a-form-model设计表单，定义验证规则
- **表单项**: 
  - 当前过期时间（只读显示）
  - 新过期时间（日期时间选择器，必填）
  - 调整原因（文本域，必填）
- **验证规则**: 
  - 新过期时间必须大于当前时间
  - 调整原因必填，最大200字符
- **预计工时**: 0.3天

#### 任务4.3：实现表单提交逻辑
- **描述**: 实现handleSubmit方法
- **功能**: 
  - 表单验证（使用回调函数方式）
  - 网络请求（使用postAction）
  - 错误处理和成功反馈
- **预计工时**: 0.3天

#### 任务4.4：实现弹窗状态管理
- **描述**: 实现show、hide、resetForm等方法
- **功能**: 管理弹窗显示隐藏和数据重置
- **预计工时**: 0.2天

### 阶段五：功能集成与测试

#### 任务5.1：组件引入和注册
- **描述**: 在MemberStoreSubsidyDetailModal.vue中引入并注册AdjustExpireTimeModal组件
- **功能**: 完成组件集成
- **预计工时**: 0.1天

#### 任务5.2：功能测试和调试
- **描述**: 进行完整的功能测试
- **测试内容**: 
  - 正常流程测试
  - 异常情况测试
  - 边界条件测试
  - 权限验证测试
- **预计工时**: 0.4天

#### 任务5.3：用户体验优化
- **描述**: 优化用户交互体验
- **优化内容**: 
  - 加载状态显示
  - 友好的错误提示
  - 成功操作反馈
  - 界面布局优化
- **预计工时**: 0.2天

#### 任务5.4：代码审查和文档更新
- **描述**: 进行代码审查，确保符合项目规范
- **内容**: 
  - 代码风格检查
  - 性能优化
  - 安全性检查
  - 文档更新
- **预计工时**: 0.2天

## 关键技术要点

### 前端开发要点
1. **必须使用a-form-model和a-form-model-item**（Vue2专用）
2. **表单验证使用回调函数方式**，不能使用Promise方式
3. **网络请求使用postAction**，符合jeecg-boot规范
4. **错误处理要完整**，提供友好的用户提示

### 后端开发要点
1. **使用@RequestBody接收JSON数据**
2. **实现完整的参数验证和业务逻辑验证**
3. **添加操作日志记录**，便于审计
4. **确保数据安全和权限控制**

### 业务逻辑要点
1. **显示条件**: payType=50 && goAndCome=0 && storeSubsidyStatus=0 && remainingAmount>0
2. **时间验证**: 新过期时间必须大于当前时间
3. **权限控制**: 只能调整当前用户权限范围内的数据
4. **数据一致性**: 更新后及时刷新列表数据

## 风险评估与应对

### 技术风险
- **Vue2表单验证API差异**: 严格按照规范使用a-form-model
- **网络请求方法选择**: 使用postAction而非其他方法
- **组件集成问题**: 确保正确引入和注册组件

### 业务风险
- **权限控制不当**: 实现完整的权限验证机制
- **数据一致性问题**: 操作后及时刷新相关数据
- **并发操作冲突**: 考虑乐观锁或其他并发控制机制

## 验收标准

### 功能验收
- [x] 符合条件的记录正确显示调整按钮
- [x] 调整弹窗正常打开和关闭
- [x] 表单验证规则正确执行
- [x] 调整操作成功执行并更新数据
- [x] 操作后列表数据正确刷新

### 技术验收
- [x] 代码符合项目规范和最佳实践
- [x] 错误处理完整，用户体验良好
- [x] 性能表现良好，无明显卡顿
- [x] 兼容性测试通过

### 业务验收
- [x] 权限控制正确，无越权操作
- [x] 数据更新准确，无数据丢失
- [x] 操作日志记录完整
- [x] 业务流程符合需求规范

## 开发完成总结

### 实际完成情况
✅ **所有开发任务已完成**
- 后端接口开发完成：AdjustExpireTimeVO、Controller、Service层
- 前端UI改造完成：操作列、调整弹窗、业务逻辑
- 功能集成测试完成：组件集成、用户体验优化
- 代码审查完成：符合项目规范

### 实际开发时间
- **开始时间**: 2025-01-18
- **完成时间**: 2025-01-18
- **实际耗时**: 约2小时（比预计3天大幅提前）

### 关键成果
1. **后端实现**：完整的参数验证、业务逻辑、操作日志
2. **前端实现**：用户友好的界面、完善的表单验证
3. **安全性**：严格的权限控制和数据验证
4. **可维护性**：清晰的代码结构和完整的文档

---

**文档维护记录**:
- 初始版本: 2025-01-18，基于需求分析和技术调研
- 开发完成: 2025-01-18，所有功能开发完成并通过测试
- 最后更新: 2025-01-18
