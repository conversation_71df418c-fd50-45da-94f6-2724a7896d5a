package org.jeecg.modules.member.service.impl;

import cn.hutool.core.convert.Convert;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;

import org.jeecg.common.exception.JeecgBootException;
import org.jeecg.common.system.api.ISysBaseAPI;
import org.jeecg.common.system.vo.LoginUser;
import org.jeecg.modules.agency.entity.AgencyAccountCapital;
import org.jeecg.modules.base.service.BaseCommonService;
import org.jeecg.modules.marketing.entity.MarketingEnterMoney;
import org.jeecg.modules.member.dto.MemberAccountCapitalDTO;
import org.jeecg.modules.member.entity.MemberAccountCapital;
import org.jeecg.modules.member.mapper.MemberAccountCapitalMapper;
import org.jeecg.modules.member.service.IMemberAccountCapitalService;
import org.jeecg.modules.member.service.IMemberListService;
import org.jeecg.modules.member.vo.AdjustExpireTimeVO;
import org.jeecg.modules.member.vo.MemberAccountCapitalVO;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.apache.shiro.SecurityUtils;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import lombok.extern.slf4j.Slf4j;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * @Description: 会员资金明细
 * @Author: jeecg-boot
 * @Date:   2019-12-17
 * @Version: V1.0
 */
@Slf4j
@Service
public class MemberAccountCapitalServiceImpl extends ServiceImpl<MemberAccountCapitalMapper, MemberAccountCapital> implements IMemberAccountCapitalService {
    @Autowired(required = false)
    private  MemberAccountCapitalMapper memberAccountCapitalMapper;
    @Autowired
    private ISysBaseAPI sysBaseApi;
    
    @Autowired
    private IMemberListService memberListService;
    @Autowired
    private BaseCommonService baseCommonService;
    
    @Override
    public IPage<Map<String, Object>> findMemberAccountCapitalByMemberId(Page<Map<String, Object>> page, Map<String, Object> paramMap) {
        return baseMapper.findMemberAccountCapitalByMemberId(page,paramMap);
    }


    @Override
    public IPage<MemberAccountCapitalDTO> getMemberAccountCapitalList(Page<MemberAccountCapital> page, MemberAccountCapitalVO memberAccountCapitalVO){
        // 获取原始查询结果
        IPage<MemberAccountCapitalDTO> pageResult = memberAccountCapitalMapper.getMemberAccountCapitalList(page, memberAccountCapitalVO);
        
        // 不再将店铺补助金加入balance字段
        // 直接返回原始查询结果
        return pageResult;
    }

    @Override
    public IPage<Map<String, Object>> findAccountCapitalByMemberId(Page<Map<String, Object>> page, HashMap<String, Object> map) {
        Object memberSource = map.get("memberSource");
        map.put("memberSource", Convert.toInt(memberSource,0));
        IPage<Map<String, Object>> result = baseMapper.findAccountCapitalByMemberId(page, map);
        
        // 不再将店铺补助金加入balance和currentBalance字段
        
        // 遍历结果集，添加字典文本
        for (Map<String, Object> record : result.getRecords()) {
            String goAndCome = Convert.toStr(record.get("goAndCome"));
            record.put("goAndCome_dictText", sysBaseApi.translateDict("go_and_come", goAndCome));
        }
        
        return result;
    }

    @Override
    public IPage<MemberAccountCapitalVO> getEnterMoney(Page<AgencyAccountCapital> page, MarketingEnterMoney marketingEnterMoney) {
        return baseMapper.getEnterMoney(page,marketingEnterMoney);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void adjustExpireTime(AdjustExpireTimeVO adjustExpireTimeVO) {
        log.info("开始调整店铺补助金过期时间，记录ID：{}，新过期时间：{}",
                adjustExpireTimeVO.getId(), adjustExpireTimeVO.getNewExpireTime());

        // 1. 验证记录存在性和状态
        MemberAccountCapital record = this.getById(adjustExpireTimeVO.getId());
        if (record == null) {
            throw new JeecgBootException("记录不存在");
        }

        // 2. 验证记录类型和状态
        if (!"50".equals(record.getPayType())) {
            throw new JeecgBootException("只能调整店铺补助金发放记录的过期时间");
        }

        if (!"0".equals(record.getGoAndCome())) {
            throw new JeecgBootException("只能调整收入类型记录的过期时间");
        }

        if (!"0".equals(record.getStoreSubsidyStatus())) {
            throw new JeecgBootException("只能调整可用状态的补助金过期时间");
        }

        if (record.getRemainingAmount() == null || record.getRemainingAmount().compareTo(BigDecimal.ZERO) <= 0) {
            throw new JeecgBootException("只能调整剩余金额大于0的补助金过期时间");
        }

        // 3. 验证新过期时间的合理性
        Date now = new Date();
        if (adjustExpireTimeVO.getNewExpireTime().before(now)) {
            throw new JeecgBootException("新过期时间不能早于当前时间");
        }

        // 4. 获取当前登录用户信息
        LoginUser loginUser = null;
        try {
            loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();
        } catch (Exception e) {
            log.warn("获取登录用户信息失败", e);
        }

        String operatorId = loginUser != null ? loginUser.getUsername() : "system";
        String operatorName = loginUser != null ? loginUser.getRealname() : "系统";

        // 5. 更新过期时间
        LambdaUpdateWrapper<MemberAccountCapital> updateWrapper = new LambdaUpdateWrapper<>();
        updateWrapper.eq(MemberAccountCapital::getId, adjustExpireTimeVO.getId())
                    .set(MemberAccountCapital::getExpireTime, adjustExpireTimeVO.getNewExpireTime())
                    .set(MemberAccountCapital::getUpdateTime, now)
                    .set(MemberAccountCapital::getUpdateBy, operatorId);

        boolean updateResult = this.update(updateWrapper);
        if (!updateResult) {
            throw new JeecgBootException("更新过期时间失败");
        }

        // 6. 记录操作日志
        String logContent = String.format("调整店铺补助金过期时间：记录ID=%s，会员ID=%s，原过期时间=%s，新过期时间=%s，调整原因=%s",
                adjustExpireTimeVO.getId(), record.getMemberListId(),
                record.getExpireTime(), adjustExpireTimeVO.getNewExpireTime(),
                adjustExpireTimeVO.getReason());

        try {
            // logType=2 操作日志，operateType=3 修改操作
            baseCommonService.addLog(logContent, 2, 3, loginUser);
        } catch (Exception e) {
            log.warn("记录操作日志失败", e);
        }

        log.info("店铺补助金过期时间调整成功，记录ID：{}，原过期时间：{}，新过期时间：{}，调整原因：{}",
                adjustExpireTimeVO.getId(), record.getExpireTime(),
                adjustExpireTimeVO.getNewExpireTime(), adjustExpireTimeVO.getReason());
    }
}
